---
import AdminLayout from '../../../layouts/AdminLayout.astro';
import { isAuthenticated } from '../../../utils/auth';
import Card from '../../../components/ui/Card.astro';
import CardHeader from '../../../components/ui/CardHeader.astro';
import CardTitle from '../../../components/ui/CardTitle.astro';
import CardDescription from '../../../components/ui/CardDescription.astro';
import CardContent from '../../../components/ui/CardContent.astro';
import Button from '../../../components/ui/Button.astro';
import Badge from '../../../components/ui/Badge.astro';
import Input from '../../../components/ui/Input.astro';
import Select from '../../../components/ui/Select.astro';
import Label from '../../../components/ui/Label.astro';

// Защита страницы
if (!isAuthenticated(Astro.request)) {
  return Astro.redirect('/admin/login');
}

// Загрузка настроек товаров
import settingsData from '../../../../data/product/settings-product.json';
---

<AdminLayout title="Настройки товаров | LuxBeton">
  <div class="space-y-8">
    <!-- Заголовок и действия -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight text-gray-900">Настройки товаров</h1>
        <p class="mt-2 text-sm text-gray-600">Управляйте типами товаров, валютами, единицами измерения и глобальными настройками</p>
      </div>
      <div class="mt-4 sm:mt-0 flex gap-3">
        <a
          href="/admin/products"
          class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          Назад к товарам
        </a>
        <button
          id="save-settings"
          class="inline-flex items-center rounded-md px-3 py-2 text-sm font-semibold text-white shadow-sm transition-colors focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
          style="background-color: #3b82f6;"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 3.75V16.5L12 14.25 7.5 16.5V3.75m9 0H18A2.25 2.25 0 0 1 20.25 6v12A2.25 2.25 0 0 1 18 20.25H6A2.25 2.25 0 0 1 3.75 18V6A2.25 2.25 0 0 1 6 3.75h1.5m9 0v0" />
          </svg>
          Сохранить настройки
        </button>
      </div>
    </div>

    <!-- Табы для разных разделов настроек -->
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8" aria-label="Tabs">
        <button
          class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
          data-tab="product-types"
        >
          Типы товаров
        </button>
        <button
          class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
          data-tab="currencies"
        >
          Валюты
        </button>
        <button
          class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
          data-tab="units"
        >
          Единицы измерения
        </button>
        <button
          class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
          data-tab="global"
        >
          Глобальные настройки
        </button>
        <button
          class="settings-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
          data-tab="payment"
        >
          Оплата
        </button>
      </nav>
    </div>

    <!-- Контент табов -->
    <div id="settings-content">
      <!-- Типы товаров -->
      <div id="product-types-content" class="tab-content">
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <div>
                <CardTitle>Типы товаров</CardTitle>
                <CardDescription>Управляйте типами товаров и их настройками</CardDescription>
              </div>
              <Button id="add-product-type" variant="outline">
                <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                </svg>
                Добавить тип
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <!-- Основной тип товара -->
            <div class="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <Label for="primary-product-type" class="text-gray-700 font-medium">Основной тип товара (по умолчанию)</Label>
              <Select id="primary-product-type" class="mt-2">
                <!-- Динамически заполняется JavaScript -->
              </Select>
              <p class="text-sm text-gray-500 mt-1">Этот тип будет выбран по умолчанию при создании новых товаров</p>
            </div>

            <div id="product-types-list" class="space-y-3">
              <!-- Динамически заполняется JavaScript -->
            </div>

            <!-- Предварительный просмотр полей для выбранного типа товара -->
            <div id="product-type-preview" class="mt-6 hidden">
              <h3 class="text-lg font-medium text-gray-900 mb-3">Предварительный просмотр полей для типа товара</h3>
              <div class="bg-gray-50 rounded-lg p-4">
                <div id="preview-content">
                  <!-- Динамически заполняется JavaScript -->
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Валюты -->
      <div id="currencies-content" class="tab-content hidden">
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <div>
                <CardTitle>Валюты</CardTitle>
                <CardDescription>Управляйте поддерживаемыми валютами и основной валютой</CardDescription>
              </div>
              <Button id="add-currency" variant="outline">
                <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                </svg>
                Добавить валюту
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div class="mb-6">
              <Label for="primary-currency" class="text-gray-700">Основная валюта</Label>
              <Select id="primary-currency" class="mt-1">
                <!-- Динамически заполняется JavaScript -->
              </Select>
            </div>
            <div id="currencies-list" class="space-y-4">
              <!-- Динамически заполняется JavaScript -->
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Оплата -->
      <div id="payment-content" class="tab-content hidden">
        <Card>
          <CardHeader>
            <CardTitle>Оплата</CardTitle>
            <CardDescription>Настройте способы и модели оплаты, а также сроки подписки</CardDescription>
          </CardHeader>
          <CardContent>
            <!-- Здесь будет динамическое наполнение JS -->
            <div id="payment-methods-list" class="mb-6"></div>
            <div id="payment-models-list" class="mb-6"></div>
            <div id="subscription-terms-list" class="mb-6"></div>
            <div id="payment-defaults" class="mb-6"></div>
            <Button id="add-payment-method" variant="outline" class="mr-2">Добавить способ оплаты</Button>
            <Button id="add-payment-model" variant="outline" class="mr-2">Добавить модель оплаты</Button>
            <Button id="add-subscription-term" variant="outline">Добавить срок подписки</Button>
          </CardContent>
        </Card>
      </div>

      <!-- Единицы измерения -->
      <div id="units-content" class="tab-content hidden">
        <Card>
          <CardHeader>
            <CardTitle>Единицы измерения</CardTitle>
            <CardDescription>Управляйте единицами измерения для разных типов товаров</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- Вес -->
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-medium text-gray-900">Вес</h3>
                  <Button data-unit-type="weight" class="add-unit-btn" variant="outline" size="sm">
                    <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                    Добавить
                  </Button>
                </div>
                <!-- Основная единица -->
                <div class="mb-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                  <Label for="primary-weight-unit" class="text-sm font-medium text-gray-700">Основная единица</Label>
                  <Select id="primary-weight-unit" class="mt-1 text-sm">
                    <!-- Динамически заполняется JavaScript -->
                  </Select>
                </div>
                <div id="weight-units" class="space-y-2">
                  <!-- Динамически заполняется JavaScript -->
                </div>
              </div>

              <!-- Объем -->
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-medium text-gray-900">Объем</h3>
                  <Button data-unit-type="volume" class="add-unit-btn" variant="outline" size="sm">
                    <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                    Добавить
                  </Button>
                </div>
                <!-- Основная единица -->
                <div class="mb-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                  <Label for="primary-volume-unit" class="text-sm font-medium text-gray-700">Основная единица</Label>
                  <Select id="primary-volume-unit" class="mt-1 text-sm">
                    <!-- Динамически заполняется JavaScript -->
                  </Select>
                </div>
                <div id="volume-units" class="space-y-2">
                  <!-- Динамически заполняется JavaScript -->
                </div>
              </div>

              <!-- Размеры -->
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-medium text-gray-900">Размеры</h3>
                  <Button data-unit-type="dimensions" class="add-unit-btn" variant="outline" size="sm">
                    <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                    Добавить
                  </Button>
                </div>
                <!-- Основная единица -->
                <div class="mb-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                  <Label for="primary-dimensions-unit" class="text-sm font-medium text-gray-700">Основная единица</Label>
                  <Select id="primary-dimensions-unit" class="mt-1 text-sm">
                    <!-- Динамически заполняется JavaScript -->
                  </Select>
                </div>
                <div id="dimensions-units" class="space-y-2">
                  <!-- Динамически заполняется JavaScript -->
                </div>
              </div>

              <!-- Штучные -->
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-medium text-gray-900">Штучные</h3>
                  <Button data-unit-type="countable" class="add-unit-btn" variant="outline" size="sm">
                    <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                    Добавить
                  </Button>
                </div>
                <!-- Основная единица -->
                <div class="mb-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                  <Label for="primary-countable-unit" class="text-sm font-medium text-gray-700">Основная единица</Label>
                  <Select id="primary-countable-unit" class="mt-1 text-sm">
                    <!-- Динамически заполняется JavaScript -->
                  </Select>
                </div>
                <div id="countable-units" class="space-y-2">
                  <!-- Динамически заполняется JavaScript -->
                </div>
              </div>

              <!-- Услуги -->
              <div class="space-y-3 lg:col-span-2">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-medium text-gray-900">Услуги</h3>
                  <Button data-unit-type="service" class="add-unit-btn" variant="outline" size="sm">
                    <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                    Добавить
                  </Button>
                </div>
                <!-- Основная единица -->
                <div class="mb-3 p-3 bg-amber-50 border border-amber-200 rounded-lg max-w-md">
                  <Label for="primary-service-unit" class="text-sm font-medium text-gray-700">Основная единица</Label>
                  <Select id="primary-service-unit" class="mt-1 text-sm">
                    <!-- Динамически заполняется JavaScript -->
                  </Select>
                </div>
                <div id="service-units" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  <!-- Динамически заполняется JavaScript -->
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Глобальные настройки -->
      <div id="global-content" class="tab-content hidden">
        <Card>
          <CardHeader>
            <CardTitle>Глобальные настройки</CardTitle>
            <CardDescription>Настройки форматирования цен, управления складом и налогов</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="space-y-6">
              <!-- Форматирование цен -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Форматирование цен</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label for="price-format" class="text-gray-700">Формат цены</Label>
                    <input id="price-format" type="text" placeholder="{currency}{amount}" class="mt-1 flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-colors" />
                  </div>
                  <div>
                    <Label for="decimal-separator" class="text-gray-700">Разделитель дробной части</Label>
                    <input id="decimal-separator" type="text" placeholder="." class="mt-1 flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-colors" />
                  </div>
                  <div>
                    <Label for="thousands-separator" class="text-gray-700">Разделитель тысяч</Label>
                    <input id="thousands-separator" type="text" placeholder="," class="mt-1 flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-colors" />
                  </div>
                </div>
              </div>

              <!-- Управление складом -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Управление складом</h3>
                <div class="space-y-4">
                  <div class="flex items-center">
                    <input id="stock-management-enabled" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
                    <Label for="stock-management-enabled" class="ml-2 text-gray-700">Включить управление складом</Label>
                  </div>
                  <div class="flex items-center">
                    <input id="allow-negative-stock" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
                    <Label for="allow-negative-stock" class="ml-2 text-gray-700">Разрешить отрицательный остаток</Label>
                  </div>
                </div>
              </div>

              <!-- Налоговые правила -->
              <div>
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-medium text-gray-900">Налоговые правила</h3>
                  <Button id="add-tax-rule" variant="outline" size="sm">
                    <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                    Добавить правило
                  </Button>
                </div>
                <div id="tax-rules-list" class="space-y-4">
                  <!-- Динамически заполняется JavaScript -->
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</AdminLayout>

<style>
  .settings-tab.active {
    border-color: #3b82f6;
    color: #3b82f6;
  }

  .tab-content {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .unit-card {
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 0.75rem;
    background: white;
    transition: all 0.2s ease-in-out;
  }

  .unit-card:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  }

  .unit-card.custom {
    border-color: #10b981;
    background: #f0fdf4;
  }

  .unit-card.primary {
    border-color: #f59e0b;
    background: #fffbeb;
  }

  .delete-btn {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
  }

  .unit-card:hover .delete-btn {
    opacity: 1;
  }

  .compact-unit-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    background: white;
    transition: all 0.2s ease-in-out;
  }

  .compact-unit-card:hover {
    border-color: #3b82f6;
    background: #f8fafc;
  }

  .compact-unit-card.primary {
    border-color: #f59e0b;
    background: #fffbeb;
  }

  .compact-unit-card.custom {
    border-color: #10b981;
    background: #f0fdf4;
  }

  .currency-card {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    background: white;
    transition: all 0.2s ease-in-out;
  }

  .currency-card:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .currency-card.primary {
    border-color: #f59e0b;
    background: #fffbeb;
  }

  .currency-card.custom {
    border-color: #10b981;
    background: #f0fdf4;
  }

  .tax-rule-card {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    background: white;
    transition: all 0.2s ease-in-out;
  }

  .tax-rule-card:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
</style>

<script define:vars={{ settingsData }}>
  // Текущие настройки
  let currentSettings = JSON.parse(JSON.stringify(settingsData));

  // Инициализация страницы
  document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    loadProductTypes();
    loadCurrencies();
    loadUnits();
    loadGlobalSettings();
    attachEventListeners();
  });

  // Инициализация табов
  function initializeTabs() {
    const tabs = document.querySelectorAll('.settings-tab');
    const firstTab = tabs[0];
    if (firstTab) {
      firstTab.classList.add('active');
    }

    tabs.forEach(tab => {
      tab.addEventListener('click', function() {
        const tabId = this.dataset.tab;
        switchTab(tabId);
      });
    });
  }

  // Переключение табов
  function switchTab(tabId) {
    // Убираем активный класс со всех табов
    document.querySelectorAll('.settings-tab').forEach(tab => {
      tab.classList.remove('active');
    });

    // Скрываем все контенты
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.add('hidden');
    });

    // Активируем выбранный таб
    const activeTab = document.querySelector(`[data-tab="${tabId}"]`);
    const activeContent = document.getElementById(`${tabId}-content`);

    if (activeTab && activeContent) {
      activeTab.classList.add('active');
      activeContent.classList.remove('hidden');
    }
  }

  // Загрузка типов товаров
  function loadProductTypes() {
    const primarySelect = document.getElementById('primary-product-type');
    const container = document.getElementById('product-types-list');

    if (!primarySelect || !container) return;

    // Заполняем селект основного типа товара
    primarySelect.innerHTML = '';
    currentSettings.product_types.supported.forEach(type => {
      const option = document.createElement('option');
      option.value = type.key;
      option.textContent = `${type.key} - ${type.label.ru}`;
      option.selected = type.key === currentSettings.product_types.primary;
      primarySelect.appendChild(option);
    });

    // Заполняем список типов товаров
    container.innerHTML = '';
    currentSettings.product_types.supported.forEach((type, index) => {
      const typeCard = createProductTypeCard(type, index);
      container.appendChild(typeCard);
    });

    // Обработчик изменения основного типа товара
    primarySelect.addEventListener('change', function() {
      currentSettings.product_types.primary = this.value;
      loadProductTypes(); // Перезагружаем для обновления визуального отображения
    });
  }

  // Создание карточки типа товара
  function createProductTypeCard(type, index) {
    const card = document.createElement('div');
    card.className = 'unit-card';

    if (type.key === currentSettings.product_types.primary) {
      card.classList.add('primary');
    }
    if (type.is_custom) {
      card.classList.add('custom');
    }

    card.innerHTML = `
      <div class="flex items-center justify-between">
        <div class="flex-1">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div>
              <label class="block text-xs font-medium text-gray-700 mb-1">Ключ</label>
              <input type="text" value="${type.key}" class="type-key w-full px-2 py-1.5 border border-gray-300 rounded text-sm" ${!type.is_custom ? 'readonly' : ''} />
            </div>
            <div>
              <label class="block text-xs font-medium text-gray-700 mb-1">Название</label>
              <input type="text" value="${type.label.ru}" class="type-label w-full px-2 py-1.5 border border-gray-300 rounded text-sm" />
            </div>
            <div>
              <label class="block text-xs font-medium text-gray-700 mb-1">Описание</label>
              <input type="text" value="${type.description.ru}" class="type-description w-full px-2 py-1.5 border border-gray-300 rounded text-sm" />
            </div>
          </div>
          ${type.key === currentSettings.product_types.primary ? '<div class="mt-2 text-xs text-amber-600 font-medium">Основной тип товара</div>' : ''}
          <div class="mt-3 flex gap-2">
            <button class="preview-btn px-2 py-1 text-xs text-blue-700 bg-blue-100 hover:bg-blue-200 rounded transition-colors" onclick="showProductTypePreview('${type.key}')">
              Показать поля
            </button>
          </div>
        </div>
        ${type.is_custom ? `
          <button class="delete-btn ml-3 px-2 py-1 text-xs text-red-700 bg-red-100 hover:bg-red-200 rounded transition-colors" onclick="deleteProductType(${index})">
            Удалить
          </button>
        ` : ''}
      </div>
    `;

    // Добавляем обработчики изменений
    const inputs = card.querySelectorAll('input');
    inputs.forEach(input => {
      input.addEventListener('input', function() {
        updateProductType(index, card);
      });
    });

    return card;
  }

  // Показать предварительный просмотр полей для типа товара
  function showProductTypePreview(productTypeKey) {
    const previewContainer = document.getElementById('product-type-preview');
    const previewContent = document.getElementById('preview-content');

    if (!previewContainer || !previewContent) return;

    // Определяем какие единицы измерения подходят для данного типа товара
    let relevantUnits = [];

    switch (productTypeKey) {
      case 'physical':
        relevantUnits = ['weight', 'volume', 'dimensions', 'countable'];
        break;
      case 'digital':
        relevantUnits = ['countable'];
        break;
      case 'service':
        relevantUnits = ['service'];
        break;
      default:
        relevantUnits = ['weight', 'volume', 'dimensions', 'countable', 'service'];
    }

    // Создаем предварительный просмотр
    let previewHTML = `
      <div class="mb-4">
        <h4 class="font-medium text-gray-900 mb-2">Доступные единицы измерения для типа "${productTypeKey}":</h4>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    `;

    relevantUnits.forEach(unitType => {
      if (currentSettings.units[unitType] && currentSettings.units[unitType].supported && currentSettings.units[unitType].supported.length > 0) {
        previewHTML += `
          <div class="bg-white rounded-lg border border-gray-200 p-3">
            <h5 class="font-medium text-gray-800 mb-2 capitalize">${getUnitTypeLabel(unitType)}</h5>
            <div class="space-y-1">
        `;

        currentSettings.units[unitType].supported.forEach(unit => {
          const isPrimary = unit.key === currentSettings.units[unitType].primary;
          previewHTML += `
            <div class="text-sm text-gray-600 ${isPrimary ? 'font-medium' : ''}">
              <span class="font-mono bg-gray-100 px-1 rounded">${unit.key}</span> - ${unit.label.ru}
              ${isPrimary ? '<span class="text-amber-600 text-xs ml-1">(основная)</span>' : ''}
            </div>
          `;
        });

        previewHTML += `
            </div>
          </div>
        `;
      }
    });

    previewHTML += `
      </div>
      <div class="mt-4 text-sm text-gray-500">
        <p>Эти единицы измерения будут доступны при создании товаров данного типа.</p>
      </div>
    `;

    previewContent.innerHTML = previewHTML;
    previewContainer.classList.remove('hidden');
  }

  // Получить название типа единицы измерения
  function getUnitTypeLabel(unitType) {
    const labels = {
      'weight': 'Вес',
      'volume': 'Объем',
      'dimensions': 'Размеры',
      'countable': 'Штучные',
      'service': 'Услуги'
    };
    return labels[unitType] || unitType;
  }

  // Обновление типа товара
  function updateProductType(index, card) {
    const keyInput = card.querySelector('.type-key');
    const labelInput = card.querySelector('.type-label');
    const descriptionInput = card.querySelector('.type-description');

    const oldKey = currentSettings.product_types.supported[index].key;
    const newKey = keyInput.value;

    currentSettings.product_types.supported[index] = {
      ...currentSettings.product_types.supported[index],
      key: newKey,
      label: { ru: labelInput.value },
      description: { ru: descriptionInput.value }
    };

    // Если изменился ключ основного типа товара, обновляем его
    if (currentSettings.product_types.primary === oldKey) {
      currentSettings.product_types.primary = newKey;
    }
  }

  // Удаление типа товара
  function deleteProductType(index) {
    const type = currentSettings.product_types.supported[index];

    if (type.key === currentSettings.product_types.primary) {
      alert('Нельзя удалить основной тип товара. Сначала выберите другой основной тип.');
      return;
    }

    if (window.adminModal) {
      window.adminModal.confirmDelete('этот тип товара').then(confirmed => {
        if (confirmed) {
          currentSettings.product_types.supported.splice(index, 1);
          loadProductTypes();
        }
      });
    } else if (confirm('Вы уверены, что хотите удалить этот тип товара?')) {
      currentSettings.product_types.supported.splice(index, 1);
      loadProductTypes();
    }
  }

  // Загрузка валют
  function loadCurrencies() {
    const primarySelect = document.getElementById('primary-currency');
    const container = document.getElementById('currencies-list');

    if (!primarySelect || !container) return;

    // Заполняем селект основной валюты
    primarySelect.innerHTML = '';
    currentSettings.currencies.supported.forEach(currency => {
      const option = document.createElement('option');
      option.value = currency.key;
      option.textContent = `${currency.key} - ${currency.label.ru}`;
      option.selected = currency.key === currentSettings.currencies.primary;
      primarySelect.appendChild(option);
    });

    // Заполняем список валют
    container.innerHTML = '';
    currentSettings.currencies.supported.forEach((currency, index) => {
      const currencyCard = createCurrencyCard(currency, index);
      container.appendChild(currencyCard);
    });

    // Обработчик изменения основной валюты
    primarySelect.addEventListener('change', function() {
      currentSettings.currencies.primary = this.value;
      loadCurrencies(); // Перезагружаем для обновления визуального отображения
    });
  }

  // Создание карточки валюты
  function createCurrencyCard(currency, index) {
    const card = document.createElement('div');
    card.className = 'currency-card';

    if (currency.key === currentSettings.currencies.primary) {
      card.classList.add('primary');
    }
    if (currency.is_custom) {
      card.classList.add('custom');
    }

    card.innerHTML = `
      <div class="flex items-center justify-between">
        <div class="flex-1">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Код валюты</label>
              <input type="text" value="${currency.key}" class="currency-key w-full px-3 py-2 border border-gray-300 rounded-md text-sm" ${!currency.is_custom ? 'readonly' : ''} />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Название</label>
              <input type="text" value="${currency.label.ru}" class="currency-label w-full px-3 py-2 border border-gray-300 rounded-md text-sm" />
            </div>
          </div>
          ${currency.key === currentSettings.currencies.primary ? '<div class="mt-2 text-sm text-amber-600 font-medium">Основная валюта</div>' : ''}
        </div>
        ${currency.is_custom ? `
          <button class="delete-btn ml-4 px-3 py-1 text-sm text-red-700 bg-red-100 hover:bg-red-200 rounded-md transition-colors" onclick="deleteCurrency(${index})">
            Удалить
          </button>
        ` : ''}
      </div>
    `;

    // Добавляем обработчики изменений
    const inputs = card.querySelectorAll('input');
    inputs.forEach(input => {
      input.addEventListener('input', function() {
        updateCurrency(index, card);
      });
    });

    return card;
  }

  // Обновление валюты
  function updateCurrency(index, card) {
    const keyInput = card.querySelector('.currency-key');
    const labelInput = card.querySelector('.currency-label');

    const oldKey = currentSettings.currencies.supported[index].key;
    const newKey = keyInput.value;

    currentSettings.currencies.supported[index] = {
      ...currentSettings.currencies.supported[index],
      key: newKey,
      label: { ru: labelInput.value }
    };

    // Если изменился ключ основной валюты, обновляем его
    if (currentSettings.currencies.primary === oldKey) {
      currentSettings.currencies.primary = newKey;
    }
  }

  // Удаление валюты
  function deleteCurrency(index) {
    const currency = currentSettings.currencies.supported[index];

    if (currency.key === currentSettings.currencies.primary) {
      alert('Нельзя удалить основную валюту. Сначала выберите другую основную валюту.');
      return;
    }

    if (window.adminModal) {
      window.adminModal.confirmDelete('эту валюту').then(confirmed => {
        if (confirmed) {
          currentSettings.currencies.supported.splice(index, 1);
          loadCurrencies();
        }
      });
    } else if (confirm('Вы уверены, что хотите удалить эту валюту?')) {
      currentSettings.currencies.supported.splice(index, 1);
      loadCurrencies();
    }
  }

  // Загрузка единиц измерения
  function loadUnits() {
    const unitTypes = ['weight', 'volume', 'dimensions', 'countable', 'service'];

    unitTypes.forEach(unitType => {
      const primarySelect = document.getElementById(`primary-${unitType}-unit`);
      const container = document.getElementById(`${unitType}-units`);

      if (!primarySelect || !container) return;

      // Заполняем селект основной единицы
      primarySelect.innerHTML = '';
      if (currentSettings.units[unitType] && currentSettings.units[unitType].supported) {
        currentSettings.units[unitType].supported.forEach(unit => {
          const option = document.createElement('option');
          option.value = unit.key;
          option.textContent = `${unit.key} - ${unit.label.ru}`;
          option.selected = unit.key === currentSettings.units[unitType].primary;
          primarySelect.appendChild(option);
        });

        // Обработчик изменения основной единицы
        primarySelect.addEventListener('change', function() {
          currentSettings.units[unitType].primary = this.value;
          loadUnits(); // Перезагружаем для обновления визуального отображения
        });

        // Заполняем список единиц
        container.innerHTML = '';
        currentSettings.units[unitType].supported.forEach((unit, index) => {
          const unitCard = createCompactUnitCard(unit, unitType, index);
          container.appendChild(unitCard);
        });
      }
    });
  }

  // Создание компактной карточки единицы измерения
  function createCompactUnitCard(unit, unitType, index) {
    const card = document.createElement('div');
    card.className = 'compact-unit-card';

    if (unit.key === currentSettings.units[unitType].primary) {
      card.classList.add('primary');
    }
    if (unit.is_custom) {
      card.classList.add('custom');
    }

    card.innerHTML = `
      <div class="flex items-center gap-3 flex-1">
        <div class="flex-1 grid grid-cols-2 gap-2">
          <input type="text" value="${unit.key}" class="unit-key px-2 py-1 border border-gray-300 rounded text-sm" ${!unit.is_custom ? 'readonly' : ''} placeholder="Ключ" />
          <input type="text" value="${unit.label.ru}" class="unit-label px-2 py-1 border border-gray-300 rounded text-sm" placeholder="Название" />
        </div>
        ${unit.key === currentSettings.units[unitType].primary ? '<span class="text-xs text-amber-600 font-medium">Основная</span>' : ''}
      </div>
      <div class="flex items-center gap-1">
        ${unit.is_custom ? `
          <button class="delete-btn px-2 py-1 text-xs text-red-700 bg-red-100 hover:bg-red-200 rounded transition-colors" onclick="deleteUnit('${unitType}', ${index})">
            Удалить
          </button>
        ` : ''}
      </div>
    `;

    // Добавляем обработчики изменений
    const inputs = card.querySelectorAll('input');
    inputs.forEach(input => {
      input.addEventListener('input', function() {
        updateUnit(unitType, index, card);
      });
    });

    return card;
  }

  // Обновление единицы измерения
  function updateUnit(unitType, index, card) {
    const keyInput = card.querySelector('.unit-key');
    const labelInput = card.querySelector('.unit-label');

    const oldKey = currentSettings.units[unitType].supported[index].key;
    const newKey = keyInput.value;

    currentSettings.units[unitType].supported[index] = {
      ...currentSettings.units[unitType].supported[index],
      key: newKey,
      label: { ru: labelInput.value }
    };

    // Если изменился ключ основной единицы, обновляем его
    if (currentSettings.units[unitType].primary === oldKey) {
      currentSettings.units[unitType].primary = newKey;
    }
  }

  // Удаление единицы измерения
  function deleteUnit(unitType, index) {
    const unit = currentSettings.units[unitType].supported[index];

    if (unit.key === currentSettings.units[unitType].primary) {
      alert('Нельзя удалить основную единицу измерения. Сначала выберите другую основную единицу.');
      return;
    }

    if (window.adminModal) {
      window.adminModal.confirmDelete('эту единицу измерения').then(confirmed => {
        if (confirmed) {
          currentSettings.units[unitType].supported.splice(index, 1);
          loadUnits();
        }
      });
    } else if (confirm('Вы уверены, что хотите удалить эту единицу измерения?')) {
      currentSettings.units[unitType].supported.splice(index, 1);
      loadUnits();
    }
  }

  // Загрузка глобальных настроек
  function loadGlobalSettings() {
    const priceFormat = document.getElementById('price-format');
    const decimalSeparator = document.getElementById('decimal-separator');
    const thousandsSeparator = document.getElementById('thousands-separator');
    const stockEnabled = document.getElementById('stock-management-enabled');
    const allowNegative = document.getElementById('allow-negative-stock');

    if (priceFormat) priceFormat.value = currentSettings.global_settings.price_format;
    if (decimalSeparator) decimalSeparator.value = currentSettings.global_settings.decimal_separator;
    if (thousandsSeparator) thousandsSeparator.value = currentSettings.global_settings.thousands_separator;
    if (stockEnabled) stockEnabled.checked = currentSettings.global_settings.stock_management.enabled;
    if (allowNegative) allowNegative.checked = currentSettings.global_settings.stock_management.allow_negative_stock;

    // Загружаем налоговые правила
    loadTaxRules();

    // Добавляем обработчики изменений
    [priceFormat, decimalSeparator, thousandsSeparator].forEach(input => {
      if (input) {
        input.addEventListener('input', updateGlobalSettings);
      }
    });

    [stockEnabled, allowNegative].forEach(checkbox => {
      if (checkbox) {
        checkbox.addEventListener('change', updateGlobalSettings);
      }
    });
  }

  // Обновление глобальных настроек
  function updateGlobalSettings() {
    const priceFormat = document.getElementById('price-format');
    const decimalSeparator = document.getElementById('decimal-separator');
    const thousandsSeparator = document.getElementById('thousands-separator');
    const stockEnabled = document.getElementById('stock-management-enabled');
    const allowNegative = document.getElementById('allow-negative-stock');

    if (priceFormat) currentSettings.global_settings.price_format = priceFormat.value;
    if (decimalSeparator) currentSettings.global_settings.decimal_separator = decimalSeparator.value;
    if (thousandsSeparator) currentSettings.global_settings.thousands_separator = thousandsSeparator.value;
    if (stockEnabled) currentSettings.global_settings.stock_management.enabled = stockEnabled.checked;
    if (allowNegative) currentSettings.global_settings.stock_management.allow_negative_stock = allowNegative.checked;
  }

  // Загрузка налоговых правил
  function loadTaxRules() {
    const container = document.getElementById('tax-rules-list');
    if (!container) return;

    container.innerHTML = '';

    currentSettings.global_settings.tax_rules.forEach((rule, index) => {
      const ruleCard = createTaxRuleCard(rule, index);
      container.appendChild(ruleCard);
    });
  }

  // Создание карточки налогового правила
  function createTaxRuleCard(rule, index) {
    const card = document.createElement('div');
    card.className = 'tax-rule-card';

    card.innerHTML = `
      <div class="flex items-center justify-between">
        <div class="flex-1">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Страна</label>
              <input type="text" value="${rule.country}" class="tax-country w-full px-3 py-2 border border-gray-300 rounded-md text-sm" />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Ставка (%)</label>
              <input type="number" value="${rule.rate}" class="tax-rate w-full px-3 py-2 border border-gray-300 rounded-md text-sm" min="0" max="100" step="0.01" />
            </div>
            <div class="flex items-center pt-6">
              <input type="checkbox" ${rule.included_in_price ? 'checked' : ''} class="tax-included h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
              <label class="ml-2 text-sm text-gray-700">Включен в цену</label>
            </div>
          </div>
        </div>
        <button class="delete-btn ml-4 px-3 py-1 text-sm text-red-700 bg-red-100 hover:bg-red-200 rounded-md transition-colors" onclick="deleteTaxRule(${index})">
          Удалить
        </button>
      </div>
    `;

    // Добавляем обработчики изменений
    const inputs = card.querySelectorAll('input');
    inputs.forEach(input => {
      input.addEventListener('input', function() {
        updateTaxRule(index, card);
      });
      input.addEventListener('change', function() {
        updateTaxRule(index, card);
      });
    });

    return card;
  }

  // Обновление налогового правила
  function updateTaxRule(index, card) {
    const countryInput = card.querySelector('.tax-country');
    const rateInput = card.querySelector('.tax-rate');
    const includedInput = card.querySelector('.tax-included');

    currentSettings.global_settings.tax_rules[index] = {
      country: countryInput.value,
      rate: parseFloat(rateInput.value) || 0,
      included_in_price: includedInput.checked
    };
  }

  // Удаление налогового правила
  function deleteTaxRule(index) {
    if (window.adminModal) {
      window.adminModal.confirmDelete('это налоговое правило').then(confirmed => {
        if (confirmed) {
          currentSettings.global_settings.tax_rules.splice(index, 1);
          loadTaxRules();
        }
      });
    } else if (confirm('Вы уверены, что хотите удалить это налоговое правило?')) {
      currentSettings.global_settings.tax_rules.splice(index, 1);
      loadTaxRules();
    }
  }

  // Подключение обработчиков событий
  function attachEventListeners() {
    // Добавление нового типа товара
    const addProductTypeBtn = document.getElementById('add-product-type');
    if (addProductTypeBtn) {
      addProductTypeBtn.addEventListener('click', function() {
        const newType = {
          key: 'new_type',
          label: { ru: 'Новый тип' },
          description: { ru: 'Описание нового типа' },
          is_custom: true
        };
        currentSettings.product_types.supported.push(newType);
        loadProductTypes();
      });
    }

    // Добавление новой валюты
    const addCurrencyBtn = document.getElementById('add-currency');
    if (addCurrencyBtn) {
      addCurrencyBtn.addEventListener('click', function() {
        const newCurrency = {
          key: 'NEW',
          label: { ru: 'Новая валюта' },
          is_custom: true
        };
        currentSettings.currencies.supported.push(newCurrency);
        loadCurrencies();
      });
    }

    // Добавление новых единиц измерения
    const addUnitBtns = document.querySelectorAll('.add-unit-btn');
    addUnitBtns.forEach(btn => {
      btn.addEventListener('click', function() {
        const unitType = this.dataset.unitType;
        const newUnit = {
          key: 'new_unit',
          label: { ru: 'Новая единица' },
          is_custom: true
        };

        if (!currentSettings.units[unitType]) {
          currentSettings.units[unitType] = {
            primary: 'new_unit',
            supported: []
          };
        }

        if (!currentSettings.units[unitType].supported) {
          currentSettings.units[unitType].supported = [];
        }

        currentSettings.units[unitType].supported.push(newUnit);
        loadUnits();
      });
    });

    // Добавление нового налогового правила
    const addTaxRuleBtn = document.getElementById('add-tax-rule');
    if (addTaxRuleBtn) {
      addTaxRuleBtn.addEventListener('click', function() {
        const newRule = {
          country: 'XX',
          rate: 0,
          included_in_price: false
        };
        currentSettings.global_settings.tax_rules.push(newRule);
        loadTaxRules();
      });
    }

    // Сохранение настроек
    const saveBtn = document.getElementById('save-settings');
    if (saveBtn) {
      saveBtn.addEventListener('click', saveSettings);
    }
  }

  // Сохранение настроек
  async function saveSettings() {
    const saveBtn = document.getElementById('save-settings');
    const originalText = saveBtn ? saveBtn.innerHTML : '';

    try {
      // Показываем индикатор загрузки
      if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.innerHTML = `
          <svg class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Сохранение...
        `;
      }

      // Валидация перед сохранением
      const validationResult = validateSettings();
      if (!validationResult.isValid) {
        throw new Error(validationResult.message);
      }

      const response = await fetch('/api/admin/settings-product', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(currentSettings)
      });

      if (response.ok) {
        if (window.showToast) {
          window.showToast('Настройки успешно сохранены', 'success');
        } else {
          alert('Настройки успешно сохранены');
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Ошибка сохранения');
      }
    } catch (error) {
      console.error('Ошибка:', error);
      if (window.adminModal) {
        window.adminModal.showConfirm({
          title: 'Ошибка',
          message: `Не удалось сохранить настройки: ${error.message}`,
          confirmText: 'ОК',
          cancelText: '',
          confirmButtonClass: 'bg-red-100 hover:bg-red-200 text-red-700 border border-red-300'
        });
      } else {
        alert(`Не удалось сохранить настройки: ${error.message}`);
      }
    } finally {
      // Восстанавливаем кнопку
      if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.innerHTML = originalText;
      }
    }
  }

  // Валидация настроек
  function validateSettings() {
    // Проверяем типы товаров
    if (!currentSettings.product_types || !currentSettings.product_types.supported || currentSettings.product_types.supported.length === 0) {
      return { isValid: false, message: 'Должен быть хотя бы один тип товара' };
    }

    // Проверяем, что основной тип товара существует
    const primaryTypeExists = currentSettings.product_types.supported.some(
      type => type.key === currentSettings.product_types.primary
    );
    if (!primaryTypeExists) {
      return { isValid: false, message: 'Основной тип товара должен существовать в списке поддерживаемых типов' };
    }

    // Проверяем уникальность ключей типов товаров
    const typeKeys = currentSettings.product_types.supported.map(type => type.key);
    const uniqueTypeKeys = [...new Set(typeKeys)];
    if (typeKeys.length !== uniqueTypeKeys.length) {
      return { isValid: false, message: 'Ключи типов товаров должны быть уникальными' };
    }

    // Проверяем валюты
    if (!currentSettings.currencies.supported || currentSettings.currencies.supported.length === 0) {
      return { isValid: false, message: 'Должна быть хотя бы одна валюта' };
    }

    // Проверяем, что основная валюта существует
    const primaryExists = currentSettings.currencies.supported.some(
      currency => currency.key === currentSettings.currencies.primary
    );
    if (!primaryExists) {
      return { isValid: false, message: 'Основная валюта должна существовать в списке поддерживаемых валют' };
    }

    // Проверяем уникальность ключей валют
    const currencyKeys = currentSettings.currencies.supported.map(currency => currency.key);
    const uniqueCurrencyKeys = [...new Set(currencyKeys)];
    if (currencyKeys.length !== uniqueCurrencyKeys.length) {
      return { isValid: false, message: 'Ключи валют должны быть уникальными' };
    }

    // Проверяем единицы измерения
    for (const unitType in currentSettings.units) {
      if (currentSettings.units[unitType] && currentSettings.units[unitType].supported) {
        // Проверяем, что основная единица существует
        const primaryUnitExists = currentSettings.units[unitType].supported.some(
          unit => unit.key === currentSettings.units[unitType].primary
        );
        if (!primaryUnitExists && currentSettings.units[unitType].supported.length > 0) {
          return { isValid: false, message: `Основная единица измерения типа "${unitType}" должна существовать в списке поддерживаемых единиц` };
        }

        // Проверяем уникальность ключей
        const unitKeys = currentSettings.units[unitType].supported.map(unit => unit.key);
        const uniqueUnitKeys = [...new Set(unitKeys)];
        if (unitKeys.length !== uniqueUnitKeys.length) {
          return { isValid: false, message: `Ключи единиц измерения типа "${unitType}" должны быть уникальными` };
        }
      }
    }

    return { isValid: true, message: '' };
  }

  // Глобальные функции для использования в onclick
  window.deleteProductType = deleteProductType;
  window.deleteCurrency = deleteCurrency;
  window.deleteUnit = deleteUnit;
  window.deleteTaxRule = deleteTaxRule;
  window.showProductTypePreview = showProductTypePreview;
</script>
