import type { APIRoute } from 'astro';
import fs from 'fs';
import path from 'path';
import { isAuthenticated } from '../../../utils/auth';

export const POST: APIRoute = async ({ request }) => {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Получаем данные из запроса
    const settings = await request.json();

    // Валидация данных
    if (!settings || typeof settings !== 'object') {
      return new Response(JSON.stringify({ error: 'Invalid data format' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Проверяем обязательные поля
    const requiredFields = ['product_types', 'currencies', 'units', 'global_settings'];
    for (const field of requiredFields) {
      if (!settings[field]) {
        return new Response(JSON.stringify({ error: `Missing required field: ${field}` }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    // Валидация типов товаров
    if (!Array.isArray(settings.product_types)) {
      return new Response(JSON.stringify({ error: 'product_types must be an array' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Валидация валют
    if (!settings.currencies.primary || !Array.isArray(settings.currencies.supported)) {
      return new Response(JSON.stringify({ error: 'Invalid currencies format' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Проверяем, что основная валюта существует в списке поддерживаемых
    const primaryCurrencyExists = settings.currencies.supported.some(
      (currency: any) => currency.key === settings.currencies.primary
    );

    if (!primaryCurrencyExists) {
      return new Response(JSON.stringify({ error: 'Primary currency must exist in supported currencies' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Валидация единиц измерения
    if (!settings.units || typeof settings.units !== 'object') {
      return new Response(JSON.stringify({ error: 'Invalid units format' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Валидация глобальных настроек
    if (!settings.global_settings || typeof settings.global_settings !== 'object') {
      return new Response(JSON.stringify({ error: 'Invalid global_settings format' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Проверяем обязательные поля в глобальных настройках
    const requiredGlobalFields = ['price_format', 'decimal_separator', 'thousands_separator', 'stock_management', 'tax_rules'];
    for (const field of requiredGlobalFields) {
      if (settings.global_settings[field] === undefined) {
        return new Response(JSON.stringify({ error: `Missing required global setting: ${field}` }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    // Валидация налоговых правил
    if (!Array.isArray(settings.global_settings.tax_rules)) {
      return new Response(JSON.stringify({ error: 'tax_rules must be an array' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Валидация каждого налогового правила
    for (const rule of settings.global_settings.tax_rules) {
      if (!rule.country || typeof rule.rate !== 'number' || typeof rule.included_in_price !== 'boolean') {
        return new Response(JSON.stringify({ error: 'Invalid tax rule format' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    // Путь к файлу настроек
    const settingsPath = path.join(process.cwd(), 'data', 'product', 'settings-product.json');

    // Создаем резервную копию
    const backupPath = path.join(process.cwd(), 'data', 'product', `settings-product.backup.${Date.now()}.json`);
    
    try {
      if (fs.existsSync(settingsPath)) {
        fs.copyFileSync(settingsPath, backupPath);
      }
    } catch (backupError) {
      console.warn('Failed to create backup:', backupError);
    }

    // Сохраняем новые настройки
    fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2), 'utf8');

    // Удаляем старые резервные копии (оставляем только последние 5)
    try {
      const dataDir = path.join(process.cwd(), 'data', 'product');
      const files = fs.readdirSync(dataDir);
      const backupFiles = files
        .filter(file => file.startsWith('settings-product.backup.'))
        .map(file => ({
          name: file,
          path: path.join(dataDir, file),
          time: fs.statSync(path.join(dataDir, file)).mtime
        }))
        .sort((a, b) => b.time.getTime() - a.time.getTime());

      // Удаляем все кроме последних 5
      if (backupFiles.length > 5) {
        for (let i = 5; i < backupFiles.length; i++) {
          fs.unlinkSync(backupFiles[i].path);
        }
      }
    } catch (cleanupError) {
      console.warn('Failed to cleanup old backups:', cleanupError);
    }

    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Settings saved successfully',
      timestamp: new Date().toISOString()
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error saving settings:', error);
    
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const GET: APIRoute = async ({ request }) => {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Путь к файлу настроек
    const settingsPath = path.join(process.cwd(), 'data', 'product', 'settings-product.json');

    if (!fs.existsSync(settingsPath)) {
      return new Response(JSON.stringify({ error: 'Settings file not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Читаем настройки
    const settingsContent = fs.readFileSync(settingsPath, 'utf8');
    const settings = JSON.parse(settingsContent);

    return new Response(JSON.stringify(settings), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error reading settings:', error);
    
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
